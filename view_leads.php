<?php

session_start();

// Redirect function for clean redirects
function redirectWithStatus($page, $status, $message) {
    header('Location: ' . $page . '?status=' . urlencode($status) . '&message=' . urlencode($message));
    exit();
}

// Auto-logout after 90 minutes (5400 seconds) of inactivity
$inactivity_timeout = 5400; // 90 minutes * 60 seconds/minute

// Check current login status and get user IDs
$is_admin_logged_in = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
$is_user_logged_in = isset($_SESSION['user_logged_in']) && $_SESSION['user_logged_in'] === true;

$logged_in_admin_id = $_SESSION['admin_id'] ?? null; // Assuming admin_id is set upon admin login
$logged_in_user_id = $_SESSION['user_id'] ?? null;   // Assuming user_id is set upon user login

// --- Handle Logout request first ---
if (isset($_GET['logout']) && $_GET['logout'] == 'true') {
    $logged_out_type = $_SESSION['logged_in_type'] ?? 'user'; // Default to user logout page
    session_unset();    // Unset all session variables
    session_destroy(); // Destroy the session

    // Redirect to the appropriate login page after logout
    if ($logged_out_type === 'admin') {
        redirectWithStatus('admin.php', 'success', 'You have been logged out.');
    } else {
        redirectWithStatus('user_login.php', 'success', 'You have been logged out.');
    }
}

// --- Enforce Login for Shared Pages ---
// If NEITHER admin NOR local user is logged in, redirect to user_login.php
if (!$is_admin_logged_in && !$is_user_logged_in) {
    redirectWithStatus('user_login.php', 'error', 'Please log in to access this page.');
}

// --- Auto-logout check for ACTIVE session (either admin or user) ---
if (($is_admin_logged_in || $is_user_logged_in) && isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > $inactivity_timeout)) {
    $logged_out_type = $_SESSION['logged_in_type'] ?? 'user'; // Capture type before unsetting
    session_unset();
    session_destroy();
    if ($logged_out_type === 'admin') {
        redirectWithStatus('admin.php', 'error', 'You were logged out due to inactivity.');
    } else {
        redirectWithStatus('user_login.php', 'error', 'You were logged out due to inactivity.');
    }
}

// Update last activity time and store user type (Crucial for auto-logout redirect)
// Only update if someone is actively logged in
if ($is_admin_logged_in) {
    $_SESSION['last_activity'] = time();
    $_SESSION['logged_in_type'] = 'admin';
} elseif ($is_user_logged_in) {
    $_SESSION['last_activity'] = time();
    $_SESSION['logged_in_type'] = 'user';
}
// --- END AUTHENTICATION LOGIC ---

// Generate CSRF token for form submission (after authentication check)
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Database configuration (same as in save_lead.php)
$db_host = 'localhost';
$db_name = 'dbnkkk8lxffmdu';
$db_user = 'uihxynu3jgkt9';
$db_pass = '@:5`|lt+1f1@';

// Establish database connection
try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    // In a production environment, you would log this error and show a generic message.
    die("Database connection failed: " . $e->getMessage());
}

// Handle search and filter parameters
$search_query = $_GET['search_query'] ?? '';
$filter_ship_date = $_GET['filter_ship_date'] ?? '';
$filter_status = $_GET['filter_status'] ?? 'All'; // Get status filter, default to 'All'

// Fetch leads from the database with search and filter, *respecting user permissions*
$leads = [];
try {
    // Start building the query
    $sql = "SELECT *,
                    DATE_FORMAT(shippment_lead.shippment_date, '%Y-%m-%d') AS formatted_shippment_date,
                    DATE_FORMAT(shippment_lead.quote_date, '%Y-%m-%d') AS formatted_quote_date,
                    DATE_FORMAT(shippment_lead.created_at, '%Y-%m-%d %H:%i:%s') AS formatted_created_at
            FROM shippment_lead WHERE 1=1";

    $params = [];

    // --- IMPORTANT: Add user-specific filter here ---
    if ($is_user_logged_in && $logged_in_user_id !== null) {
        // If a regular user is logged in, show only their leads.
        // ASSUMPTION: shippment_lead table has a 'user_id' column
        $sql .= " AND shippment_lead.user_id = :current_user_id";
        $params[':current_user_id'] = $logged_in_user_id;
    }
    // If an admin is logged in, no additional user_id filter is applied,
    // so they will see all leads (as per the default behavior).

    // Add general search query conditions for specified fields
    if (!empty($search_query)) {
        // Build the OR conditions for each searchable field
        $search_conditions = [];
        $search_value = '%' . htmlspecialchars($search_query) . '%'; // Prepare search term once

        $search_conditions[] = "CAST(shippment_lead.id AS CHAR) LIKE :search_query_id";
        $params[':search_query_id'] = $search_value;

        $search_conditions[] = "shippment_lead.name LIKE :search_query_name";
        $params[':search_query_name'] = $search_value;

        $search_conditions[] = "shippment_lead.email LIKE :search_query_email";
        $params[':search_query_email'] = $search_value;

        $search_conditions[] = "shippment_lead.phone LIKE :search_query_phone";
        $params[':search_query_phone'] = $search_value;

        $search_conditions[] = "CAST(shippment_lead.quote_amount AS CHAR) LIKE :search_query_quote_amount";
        $params[':search_query_quote_amount'] = $search_value;

        $search_conditions[] = "DATE_FORMAT(shippment_lead.shippment_date, '%Y-%m-%d') LIKE :search_query_shippment_date";
        $params[':search_query_shippment_date'] = $search_value;

        $search_conditions[] = "shippment_lead.status LIKE :search_query_status";
        $params[':search_query_status'] = $search_value;

        $search_conditions[] = "shippment_lead.quote_id LIKE :search_query_quote_id";
        $params[':search_query_quote_id'] = $search_value;

        // Combine all search conditions with OR
        $sql .= " AND (" . implode(" OR ", $search_conditions) . ")";
    }

    // Add shipment date filter (this remains separate and exact)
    if (!empty($filter_ship_date)) {
        $sql .= " AND shippment_lead.shippment_date = :filter_ship_date";
        $params[':filter_ship_date'] = htmlspecialchars($filter_ship_date);
    }

    // New: Add status filter condition
    if (!empty($filter_status) && $filter_status !== 'All') {
        $sql .= " AND shippment_lead.status = :filter_status";
        $params[':filter_status'] = htmlspecialchars($filter_status);
    }

    // Order by created_at (time of insertion) for most recent at top
    $sql .= " ORDER BY shippment_lead.created_at DESC, shippment_lead.id DESC";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $leads = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    die("Error fetching leads: " . $e->getMessage());
}

// Handle success/error messages from other pages (e.g., update_lead.php, delete_lead.php)
$status_message = '';
$status_type = '';
if (isset($_GET['status']) && isset($_GET['message'])) {
    $status_type = htmlspecialchars($_GET['status']);
    $status_message = htmlspecialchars(urldecode($_GET['message']));
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View All Leads - MJ Hauling United LLC</title>
    <style>
        :root {
            --primary-color: #2c73d2;
            --primary-dark: #1a4b8c;
            --secondary-color: #28a745;
            --danger-color: #d9534f;
            --danger-dark: #c9302c;
            --gray-color: #6c757d;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --white: #ffffff;
            --shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            --transition: all 0.3s ease;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f0f2f5;
            padding: 0;
            margin: 0;
            color: #333;
            padding-top: 70px;
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* --- Navbar Styles --- */
        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: var(--shadow);
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            z-index: 1000;
            box-sizing: border-box;
            height: 70px;
        }
        
        .navbar-brand {
            display: flex;
            align-items: center;
            height: 100%;
        }
        
        .navbar-logo {
            height: 50px;
            width: auto;
            object-fit: contain;
            margin-right: 10px;
        }
        
        .navbar-container {
            display: flex;
            height: 100%;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }
        
        .navbar-nav {
            display: flex;
            align-items: center;
            height: 100%;
            margin: 0;
            padding: 0;
            list-style: none;
        }
        
        .nav-item {
            height: 100%;
            display: flex;
            align-items: center;
            position: relative;
        }
        
        .nav-link {
            color: var(--white);
            text-decoration: none;
            padding: 0 15px;
            height: 100%;
            display: flex;
            align-items: center;
            transition: var(--transition);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .nav-link i {
            margin-right: 8px;
            font-size: 1.1rem;
        }
        
        .logout-btn {
            background-color: var(--danger-color);
            color: var(--white);
            border: none;
            padding: 0 20px;
            height: 100%;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: var(--transition);
            font-weight: 500;
        }
        
        .logout-btn:hover {
            background-color: var(--danger-dark);
        }
        
        .logout-btn i {
            margin-right: 8px;
        }
        
        .search-container {
            display: flex;
            align-items: center;
            margin-left: auto;
            padding: 0 15px;
            height: 100%;
        }
        
        .search-form {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .search-form input,
        .search-form select,
        .search-form button {
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            background-color: rgba(255, 255, 255, 0.9);
            transition: var(--transition);
        }
        
        .search-form input:focus,
        .search-form select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(44, 115, 210, 0.3);
        }
        
        .search-form button {
            background-color: var(--secondary-color);
            color: var(--white);
            border: none;
            cursor: pointer;
            font-weight: 500;
        }
        
        .search-form button:hover {
            background-color: #218838;
        }
        
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: var(--white);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 10px;
        }

        /* --- Responsive Adjustments --- */
        @media (max-width: 1200px) {
            .search-form {
                flex-wrap: wrap;
            }
            
            .search-form input,
            .search-form select {
                width: 120px;
            }
        }

        @media (max-width: 992px) {
            .mobile-menu-btn {
                display: block;
            }
            
            .navbar-container {
                position: fixed;
                top: 70px;
                left: 0;
                width: 100%;
                background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
                flex-direction: column;
                height: auto;
                max-height: 0;
                overflow: hidden;
                transition: max-height 0.3s ease;
                box-shadow: 0 10px 15px rgba(0,0,0,0.1);
            }
            
            .navbar-container.active {
                max-height: 500px; /* Adjust as needed */
                padding: 15px 0;
            }
            
            .navbar-nav {
                flex-direction: column;
                width: 100%;
                height: auto;
            }
            
            .nav-item {
                width: 100%;
                height: auto;
            }
            
            .nav-link {
                padding: 12px 20px;
                width: 100%;
                justify-content: flex-start;
            }
            
            .logout-btn {
                width: 100%;
                padding: 12px 20px;
                justify-content: flex-start;
                border-radius: 0;
            }
            
            .search-container {
                width: 100%;
                padding: 15px 20px;
                margin-left: 0;
            }
            
            .search-form {
                width: 100%;
            }
            
            .search-form input,
            .search-form select,
            .search-form button {
                width: 100%;
            }
        }

        @media (max-width: 576px) {
            .navbar {
                padding: 0 10px;
            }
            
            .navbar-logo {
                height: 40px;
            }
        }

        /* --- Main Container & Status Message --- */
        .container {
            background: #fff;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
            width: 100%;
            max-width: 1400px;
            margin: 20px auto;
        }
        .status-message {
            text-align: center;
            padding: 15px;
            margin: 20px 0;
            border-radius: 10px;
            font-weight: bold;
            display: <?php echo !empty($status_message) ? 'block' : 'none'; ?>;
            animation: fadeIn 0.5s forwards;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }
        .success {
            background-color: #e6ffed;
            color: #1a7e3d;
            border: 1px solid #a8e6b9;
        }
        .error {
            background-color: #ffe6e6;
            color: #d63333;
            border: 1px solid #ffb3b3;
        }
        .warning {
            background-color: #fff8e6;
            color: #b3771a;
            border: 1px solid #ffe0b3;
        }

        /* --- Table Styles --- */
        .table-responsive-wrapper {
            width: 100%;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            box-shadow: inset 0 0 5px rgba(0,0,0,0.02);
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
            min-width: 800px;
        }
        th, td {
            border: 1px solid #eee;
            padding: 10px 12px;
            text-align: left;
            vertical-align: middle;
            word-wrap: break-word;
            white-space: normal;
        }
        th {
            background-color: #eef2f7;
            color: #4a5568;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.8em;
            position: sticky;
            left: 0;
            z-index: 2;
        }
        tr:nth-child(even) {
            background-color: #f8fbfd;
        }
        tr:hover {
            background-color: #eef7ff;
            cursor: pointer;
        }
        td.actions {
            white-space: normal;
            min-width: 180px;
            text-align: center;
        }
        .actions a {
            margin-right: 8px;
            text-decoration: none;
            padding: 6px 12px;
            border-radius: 5px;
            font-size: 0.85em;
            white-space: nowrap;
            display: inline-block;
            margin-bottom: 5px;
            transition: background-color 0.3s ease, transform 0.2s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.08);
        }
        .actions .edit-btn {
            background-color: #007bff;
            color: white;
        }
        .actions .edit-btn:hover {
            background-color: #0056b3;
            transform: translateY(-1px);
        }
        .no-leads {
            text-align: center;
            padding: 30px;
            font-style: italic;
            color: #777;
            background-color: #fefefe;
            border-radius: 8px;
            margin-top: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .total-entries {
            text-align: left;
            margin-top: 15px;
            margin-bottom: 10px;
            font-weight: bold;
            color: #555;
            padding-left: 5px;
        }
        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        footer {
            text-align: center;
            margin-top: 4rem;
            font-weight: 600;
            color: #888;
            font-size: 0.9rem;
            padding-bottom: 20px;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script>
        // Function to confirm deletion (kept for safety, though button is removed)
        function confirmDelete(id) {
            return confirm('Are you sure you want to delete this lead (ID: ' + id + ')? This action cannot be undone.');
        }

        // Function to display status messages
        window.onload = function() {
            const statusDiv = document.getElementById('statusMessage');
            if (statusDiv.style.display === 'block') {
                setTimeout(() => statusDiv.style.display = 'none', 5000);
            }
            
            // Toggle mobile menu
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
            const navbarContainer = document.querySelector('.navbar-container');
            
            mobileMenuBtn.addEventListener('click', function() {
                navbarContainer.classList.toggle('active');
            });
        };

        // Function to reset search and filter form
        function resetFilters() {
            window.location.href = 'view_leads.php';
        }
    </script>
</head>
<body>
    <div class="navbar">
        <div class="navbar-brand">
            <img class="navbar-logo" src="assets/img/logo/logo.png" alt="MJ Hauling United LLC">
        </div>
        
        <button class="mobile-menu-btn">
            <i class="fas fa-bars"></i>
        </button>
        
        <div class="navbar-container">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a href="shippment_lead.php" class="nav-link">
                        <i class="fas fa-plus-circle"></i> New Lead
                    </a>
                </li>
            </ul>
            
            <div class="search-container">
                <form method="GET" action="view_leads.php" class="search-form">
                    <input type="text" id="search_query" name="search_query" placeholder="Search leads..." value="<?php echo htmlspecialchars($search_query); ?>">
                    <input type="date" id="filter_ship_date" name="filter_ship_date" title="Filter by Shipment Date" value="<?php echo htmlspecialchars($filter_ship_date); ?>">
                    <select id="filter_status" name="filter_status">
                        <option value="All" <?php echo ($filter_status == 'All') ? 'selected' : ''; ?>>All Status</option>
                        <option value="Booked" <?php echo ($filter_status == 'Booked') ? 'selected' : ''; ?>>Booked</option>
                        <option value="Not Pick" <?php echo ($filter_status == 'Not Pick') ? 'selected' : ''; ?>>Not Pick</option>
                        <option value="Voice Mail" <?php echo ($filter_status == 'Voice Mail') ? 'selected' : ''; ?>>Voice Mail</option>
                        <option value="In Future Shipment" <?php echo ($filter_status == 'In Future Shipment') ? 'selected' : ''; ?>>In Future Shipment</option>
                        <option value="Quotation" <?php echo ($filter_status == 'Quotation') ? 'selected' : ''; ?>>Quotation</option>
                        <option value="Invalid Lead" <?php echo ($filter_status == 'Invalid Lead') ? 'selected' : ''; ?>>Invalid Lead</option>
                        <option value="Stop Lead" <?php echo ($filter_status == 'Stop Lead') ? 'selected' : ''; ?>>Stop Lead</option>
                        <option value="Already Booked"<?php echo ($filter_status == 'Already Booked') ? 'selected' : ''; ?>>Already Booked</option>
                        <option value="Delivered"<?php echo ($filter_status == 'Delivered') ? 'selected' : ''; ?>>Delivered</option>
                    </select>
                    <button type="submit"><i class="fas fa-search"></i> Search</button>
                    <button type="button" onclick="resetFilters()"><i class="fas fa-undo"></i> Reset</button>
                </form>
            </div>
            
            <button class="logout-btn" onclick="window.location.href='?logout=true'">
                <i class="fas fa-sign-out-alt"></i> Logout
            </button>
        </div>
    </div>

    <div class="container">
        <div id="statusMessage" class="status-message <?php echo $status_type; ?>">
            <?php echo $status_message; ?>
        </div>

        <?php if (count($leads) > 0): ?>
            <div class="total-entries">Total Entries: <?php echo count($leads); ?></div>
            <div class="table-responsive-wrapper">
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Quote ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Quote Amount</th>
                            <th>Quote Date</th>
                            <th>Ship Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($leads as $lead): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($lead['id']); ?></td>
                                <td><?php echo htmlspecialchars($lead['quote_id']); ?></td>
                                <td><?php echo htmlspecialchars($lead['name']); ?></td>
                                <td><?php echo htmlspecialchars($lead['email']); ?></td>
                                <td><?php echo htmlspecialchars($lead['phone']); ?></td>
                                <td>$<?php echo htmlspecialchars(number_format($lead['quote_amount'], 2)); ?></td>
                                <td><?php echo htmlspecialchars($lead['formatted_quote_date']); ?></td>
                                <td><?php echo htmlspecialchars($lead['formatted_shippment_date']); ?></td>
                                <td><?php echo htmlspecialchars($lead['status']); ?></td>
                                <td class="actions">
                                    <a href="edit_lead.php?id=<?php echo htmlspecialchars($lead['id']); ?>" class="edit-btn">View Details & Edit</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <p class="no-leads">No leads found matching your criteria.</p>
        <?php endif; ?>
    </div>
    <footer>Powered by Desired Technologies</footer>
</body>
</html>