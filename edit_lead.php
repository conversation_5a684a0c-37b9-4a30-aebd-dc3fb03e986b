<?php
session_start();

// Redirect function
function redirectWithStatus($page, $status, $message) {
    header('Location: ' . $page . '?status=' . urlencode($status) . '&message=' . urlencode($message));
    exit();
}

// Check authentication and get user information
$is_admin_logged_in = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
$is_user_logged_in = isset($_SESSION['user_logged_in']) && $_SESSION['user_logged_in'] === true;

$logged_in_admin_id = $_SESSION['admin_id'] ?? null;
$logged_in_user_id = $_SESSION['user_id'] ?? null;

// Enforce login requirement
if (!$is_admin_logged_in && !$is_user_logged_in) {
    redirectWithStatus('user_login.php', 'error', 'Please log in to access this page.');
}

// Database configuration
$db_host = 'localhost';
$db_name = 'dbnkkk8lxffmdu';
$db_user = 'uihxynu3jgkt9';
$db_pass = '@:5`|lt+1f1@';

// Establish database connection
try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    redirectWithStatus('view_leads.php', 'error', "Database connection error: " . $e->getMessage());
}

// Handle POST request (when form is submitted)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Check CSRF token
    if (!isset($_POST['csrf_token']) || !isset($_SESSION['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        redirectWithStatus('view_leads.php', 'error', 'Invalid security token. Please try again.');
    }

    // Get ID from POST
    $current_lead_id = $_POST['id'] ?? null;

    if (!$current_lead_id || !is_numeric($current_lead_id)) {
        redirectWithStatus('view_leads.php', 'error', 'Invalid lead ID provided for updating.');
    }

    // Verify user has permission to edit this lead before proceeding
    try {
        $permission_sql = "SELECT id, user_id FROM shippment_lead WHERE id = :id";
        $permission_params = [':id' => $current_lead_id];

        // If regular user is logged in, ensure they can only edit their own leads
        if ($is_user_logged_in && $logged_in_user_id !== null) {
            $permission_sql .= " AND user_id = :user_id";
            $permission_params[':user_id'] = $logged_in_user_id;
        }

        $permission_stmt = $pdo->prepare($permission_sql);
        $permission_stmt->execute($permission_params);
        $permission_check = $permission_stmt->fetch(PDO::FETCH_ASSOC);

        if (!$permission_check) {
            if ($is_user_logged_in && !$is_admin_logged_in) {
                redirectWithStatus('view_leads.php', 'error', 'You do not have permission to edit this lead.');
            } else {
                redirectWithStatus('view_leads.php', 'error', 'Lead not found for updating.');
            }
        }
    } catch (PDOException $e) {
        redirectWithStatus('view_leads.php', 'error', "Permission check failed: " . $e->getMessage());
    }

    // Collect form data
    $name = $_POST['name'] ?? '';
    $email = $_POST['email'] ?? '';
    $phone = $_POST['phone'] ?? '';
    $quote_amount = $_POST['quote_amount'] ?? 0;
    $quote_id = $_POST['quote_id'] ?? '';
    $quote_date = $_POST['quote_date'] ?? null;
    $shippment_date = $_POST['shippment_date'] ?? null;
    $status = $_POST['status'] ?? '';
    $year = $_POST['year'] ?? '';
    $make = $_POST['make'] ?? '';
    $model = $_POST['model'] ?? '';
    $pickup_city = $_POST['pickup_city'] ?? '';
    $pickup_state = $_POST['pickup_state'] ?? '';
    $pickup_zip = $_POST['pickup_zip'] ?? '';
    $delivery_city = $_POST['delivery_city'] ?? '';
    $delivery_state = $_POST['delivery_state'] ?? '';
    $delivery_zip = $_POST['delivery_zip'] ?? '';
    $formatted_message = $_POST['formatted_message'] ?? '';

    // Validate required fields
    if (empty($name) || empty($quote_id) || empty($quote_date) || !is_numeric($quote_amount)) {
        // Generate new CSRF token for retry
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        redirectWithStatus('edit_lead.php?id=' . urlencode($current_lead_id), 'error', 'Missing required fields or invalid data. Please check and try again.');
    }

    // Update the lead with permission check in WHERE clause
    $sql = "UPDATE shippment_lead SET
                name = :name,
                email = :email,
                phone = :phone,
                quote_amount = :quote_amount,
                quote_id = :quote_id,
                quote_date = :quote_date,
                shippment_date = :shippment_date,
                status = :status,
                year = :year,
                make = :make,
                model = :model,
                pickup_city = :pickup_city,
                pickup_state = :pickup_state,
                pickup_zip = :pickup_zip,
                delivery_city = :delivery_city,
                delivery_state = :delivery_state,
                delivery_zip = :delivery_zip,
                formatted_message = :formatted_message,
                updated_at = NOW()
            WHERE id = :id";

    $update_params = [
        ':name' => $name,
        ':email' => $email,
        ':phone' => $phone,
        ':quote_amount' => (float)$quote_amount,
        ':quote_id' => $quote_id,
        ':quote_date' => $quote_date,
        ':shippment_date' => $shippment_date,
        ':status' => $status,
        ':year' => $year,
        ':make' => $make,
        ':model' => $model,
        ':pickup_city' => $pickup_city,
        ':pickup_state' => $pickup_state,
        ':pickup_zip' => $pickup_zip,
        ':delivery_city' => $delivery_city,
        ':delivery_state' => $delivery_state,
        ':delivery_zip' => $delivery_zip,
        ':formatted_message' => $formatted_message,
        ':id' => $current_lead_id
    ];

    // Add user permission check to UPDATE query for regular users
    if ($is_user_logged_in && $logged_in_user_id !== null) {
        $sql .= " AND user_id = :user_id";
        $update_params[':user_id'] = $logged_in_user_id;
    }

    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($update_params);

        // Clear CSRF token after successful update
        unset($_SESSION['csrf_token']);

        if ($stmt->rowCount() > 0) {
            redirectWithStatus('view_leads.php', 'success', 'Lead updated successfully!');
        } else {
            // Check if this is due to permission issues or no actual changes
            if ($is_user_logged_in && !$is_admin_logged_in) {
                redirectWithStatus('view_leads.php', 'error', 'Update failed. You may not have permission to edit this lead or the lead was not found.');
            } else {
                redirectWithStatus('view_leads.php', 'info', 'No changes were made to the lead.');
            }
        }

    } catch (PDOException $e) {
        // Generate new CSRF token for retry
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        redirectWithStatus('edit_lead.php?id=' . urlencode($current_lead_id), 'error', 'Database error: ' . $e->getMessage());
    }
}

// Handle GET request (when loading the edit form)
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    redirectWithStatus('view_leads.php', 'error', 'Invalid lead ID provided for editing.');
}

$current_lead_id = $_GET['id'];

// Fetch current lead data with permission checking
try {
    // Build query with permission check
    $sql = "SELECT * FROM shippment_lead WHERE id = :id";
    $params = [':id' => $current_lead_id];

    // If regular user is logged in, ensure they can only edit their own leads
    if ($is_user_logged_in && $logged_in_user_id !== null) {
        $sql .= " AND user_id = :user_id";
        $params[':user_id'] = $logged_in_user_id;
    }
    // Admin can edit any lead, so no additional restriction needed

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $lead_data = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$lead_data) {
        if ($is_user_logged_in && !$is_admin_logged_in) {
            redirectWithStatus('view_leads.php', 'error', 'Lead not found or you do not have permission to edit this lead.');
        } else {
            redirectWithStatus('view_leads.php', 'error', 'Lead not found.');
        }
    }

} catch (PDOException $e) {
    redirectWithStatus('view_leads.php', 'error', "Error fetching lead data: " . $e->getMessage());
}

// Generate CSRF token
$_SESSION['csrf_token'] = bin2hex(random_bytes(32));

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Lead - MJ Hauling United LLC</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f0f4f8 0%, #dbe2ed 100%);
            padding: 0;
            margin: 0;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            padding-top: 60px;
            box-sizing: border-box;
            overflow-x: hidden;
        }

        .navbar {
            background: #2c73d2;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            flex-wrap: wrap;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            z-index: 1000;
            border-radius: 0;
            box-sizing: border-box;
        }
        .navbar .site-title {
            font-size: 1.6rem;
            font-weight: 700;
            color: white;
            letter-spacing: 0.5px;
            flex-shrink: 0;
            margin-right: 20px;
        }
        .navbar-links {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        .navbar-links a {
            color: white;
            text-decoration: none;
            padding: 10px 18px;
            border-radius: 6px;
            transition: background-color 0.3s ease, transform 0.2s ease;
            font-weight: 500;
            white-space: nowrap;
        }
        .navbar-links a:hover {
            background-color: #3e81e0;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .navbar {
                flex-direction: column;
                align-items: flex-start;
                padding: 15px 10px;
            }
            .navbar .site-title {
                font-size: 1.5rem;
                margin-bottom: 10px;
                width: 100%;
                text-align: center;
                margin-right: 0;
            }
            .navbar-links {
                width: 100%;
                justify-content: center;
                gap: 10px;
            }
            .navbar-links a {
                padding: 8px 12px;
                font-size: 0.95rem;
            }
            body {
                padding-top: 120px;
            }
        }
        @media (max-width: 480px) {
            .navbar-links a {
                padding: 6px 10px;
                font-size: 0.9rem;
            }
            body {
                padding-top: 100px;
            }
        }

        .container {
            background: #fff;
            padding: 3rem 5%;
            border-radius: 15px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            width: 100%;
            max-width: none;
            margin-top: 0;
            margin-bottom: 2rem;
            box-sizing: border-box;
        }
        h1 {
            text-align: center;
            color: #2c73d2;
            margin-top: 0;
            padding-top: -0.9rem;
            margin-bottom: 0.5rem;
            font-size: 2.5rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.05);
        }
        .status-message {
            text-align: center;
            padding: 15px;
            margin: 20px 0;
            border-radius: 10px;
            font-weight: bold;
            display: none;
            animation: fadeIn 0.5s forwards;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .form-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 1.5rem;
        }
        .form-group {
            flex-grow: 1;
            flex-shrink: 1;
            flex-basis: calc(25% - 15px);
            min-width: 180px;
        }

        .form-group.span-3-columns {
            flex-basis: calc(75% - 15px);
            min-width: 580px;
        }

        .form-group.buttons-stacked {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }

        label {
            display: block;
            margin-bottom: 0.6rem;
            font-weight: 600;
            color: #444;
            font-size: 1.05rem;
        }
        textarea, input[type="text"], input[type="date"], input[type="number"], input[type="email"], select {
            width: 100%;
            padding: 14px;
            font-size: 1.05rem;
            border: 1px solid #aed6f1;
            border-radius: 8px;
            box-sizing: border-box;
            transition: border-color 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
            background-color: #fcfdff;
        }
        textarea {
            min-height: 120px;
            resize: vertical;
        }
        textarea:focus, input:focus, select:focus {
            border-color: #2c73d2;
            outline: none;
            box-shadow: 0 0 0 4px rgba(44, 115, 210, 0.25);
        }

        .form-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-top: 15px;
            width: 100%;
        }

        .form-buttons button {
            background: #4a90e2;
            color: white;
            border: none;
            padding: 14px 30px;
            font-size: 1.2rem;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s ease-in-out, transform 0.1s ease-in-out, box-shadow 0.2s ease;
            font-weight: 600;
            width: 100%;
            max-width: none;
        }
        .form-buttons button:hover {
            background-color: #357ABD;
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        }
        .form-buttons button:active {
            transform: translateY(0);
            box-shadow: none;
        }
        .form-buttons .cancel-btn {
            background: #6c757d;
        }
        .form-buttons .cancel-btn:hover {
            background-color: #5a6268;
        }

        footer {
            text-align: center;
            margin-top: auto;
            padding-top: 2rem;
            font-weight: bold;
            color: #777;
            font-size: 0.9rem;
            width: 100%;
            padding-bottom: 20px;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @media (min-width: 1200px) {
            .form-row .form-group {
                flex-basis: calc(25% - 15px);
            }
            .form-group.span-3-columns {
                flex-basis: calc(75% - 15px);
            }
        }

        @media (max-width: 1199px) {
            .form-row .form-group {
                flex-basis: calc(33.333% - 13.333px);
            }
            .form-group.span-3-columns {
                flex-basis: calc(100% - 0px);
            }
            .form-group.buttons-stacked {
                flex-basis: calc(33.333% - 13.333px);
            }
        }

        @media (max-width: 991px) {
            .form-row .form-group {
                flex-basis: calc(50% - 10px);
            }
            .form-group.span-3-columns {
                flex-basis: calc(100% - 0px);
            }
            .form-group.buttons-stacked {
                flex-basis: calc(50% - 10px);
            }
        }

        @media (max-width: 767px) {
            .container {
                padding: 2rem 3%;
            }
            .form-row .form-group {
                flex-basis: calc(100% - 0px);
                min-width: unset;
            }
            .form-group.span-3-columns, .form-group.buttons-stacked {
                flex-basis: calc(100% - 0px);
            }
            .form-buttons button {
                min-width: unset;
                width: 100%;
                max-width: none;
            }
            body {
                padding-top: 120px;
            }
        }

        @media (max-width: 575px) {
            .container {
                padding: 1.5rem 2%;
            }
            .form-row {
                gap: 0;
            }
            label {
                font-size: 0.95rem;
                margin-bottom: 0.4rem;
            }
            input[type="text"], input[type="date"], input[type="number"], input[type="email"], select, textarea {
                padding: 10px;
                font-size: 0.9rem;
                margin-bottom: 0.8rem;
            }
            .form-buttons button {
                padding: 12px 20px;
                font-size: 1.1rem;
            }
            body {
                padding-top: 100px;
            }
        }
    </style>
</head>
<body>
    <div class="navbar">
        <span class="site-title">MJ Hauling United LLC</span>
        <div class="navbar-links">
            <a href="shippment_lead.php">New Lead Form</a>
            <a href="view_leads.php">View All Leads</a>
        </div>
    </div>

    <div class="container">
        <h1>Edit Lead (ID: <?php echo htmlspecialchars($lead_data['id']); ?>)</h1>

        <div id="statusMessage" class="status-message"></div>

        <form id="editLeadForm" method="post">
            <input type="hidden" name="id" value="<?php echo htmlspecialchars($lead_data['id']); ?>">
            <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($_SESSION['csrf_token']); ?>">

            <div class="form-row">
                <div class="form-group">
                    <label for="name">Name:</label>
                    <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($lead_data['name']); ?>" required>
                </div>
                <div class="form-group">
                    <label for="email">Email:</label>
                    <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($lead_data['email']); ?>">
                </div>
                <div class="form-group">
                    <label for="phone">Phone:</label>
                    <input type="text" id="phone" name="phone" value="<?php echo htmlspecialchars($lead_data['phone']); ?>">
                </div>
                <div class="form-group">
                    <label for="quote_amount">Quote Amount ($):</label>
                    <input type="number" id="quote_amount" name="quote_amount" value="<?php echo htmlspecialchars($lead_data['quote_amount']); ?>" step="0.01" required>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="quote_id">Quote ID:</label>
                    <input type="text" id="quote_id" name="quote_id" value="<?php echo htmlspecialchars($lead_data['quote_id']); ?>" required>
                </div>
                <div class="form-group">
                    <label for="quote_date">Quote Date:</label>
                    <input type="date" id="quote_date" name="quote_date" value="<?php echo htmlspecialchars($lead_data['quote_date']); ?>" required>
                </div>
                <div class="form-group">
                    <label for="shippment_date">Shipment Date:</label>
                    <input type="date" id="shippment_date" name="shippment_date" value="<?php echo htmlspecialchars($lead_data['shippment_date']); ?>">
                </div>
                <div class="form-group">
                    <label for="status">Status:</label>
                    <select id="status" name="status">
                        <option value="">-- Select Status --</option>
                        <option value="Booked" <?php echo ($lead_data['status'] == 'Booked') ? 'selected' : ''; ?>>Booked</option>
                        <option value="Not Pick" <?php echo ($lead_data['status'] == 'Not Pick') ? 'selected' : ''; ?>>Not Pick</option>
                        <option value="Voice Mail" <?php echo ($lead_data['status'] == 'Voice Mail') ? 'selected' : ''; ?>>Voice Mail</option>
                        <option value="In Future Shipment" <?php echo ($lead_data['status'] == 'In Future Shipment') ? 'selected' : ''; ?>>In Future Shipment</option>
                        <option value="Qutation" <?php echo ($lead_data['status'] == 'Qutation') ? 'selected' : ''; ?>>Quotation</option>
                        <option value="Invalid Lead" <?php echo ($lead_data['status'] == 'Invalid Lead') ? 'selected' : ''; ?>>Invalid Lead</option>
                        <option value="Stop Lead" <?php echo ($lead_data['status'] == 'Stop Lead') ? 'selected' : ''; ?>>Stop Lead</option>
                        <option value="Already Booked"<?php echo ($lead_data['status'] == 'Already Booked') ? 'selected' : '';?>>Already Booked</option>
                        <option value="Delivered"<?php echo ($lead_data['status'] == 'Delivered') ? 'selected' : '';?>>Delivered</option>
                    </select>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="year">Vehicle Year:</label>
                    <input type="text" id="year" name="year" value="<?php echo htmlspecialchars($lead_data['year']); ?>">
                </div>
                <div class="form-group">
                    <label for="make">Vehicle Make:</label>
                    <input type="text" id="make" name="make" value="<?php echo htmlspecialchars($lead_data['make']); ?>">
                </div>
                <div class="form-group">
                    <label for="model">Vehicle Model:</label>
                    <input type="text" id="model" name="model" value="<?php echo htmlspecialchars($lead_data['model']); ?>">
                </div>
                <div class="form-group">
                    <label for="pickup_city">Pickup City:</label>
                    <input type="text" id="pickup_city" name="pickup_city" value="<?php echo htmlspecialchars($lead_data['pickup_city']); ?>">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="pickup_state">Pickup State:</label>
                    <input type="text" id="pickup_state" name="pickup_state" value="<?php echo htmlspecialchars($lead_data['pickup_state']); ?>">
                </div>
                <div class="form-group">
                    <label for="pickup_zip">Pickup Zip:</label>
                    <input type="text" id="pickup_zip" name="pickup_zip" value="<?php echo htmlspecialchars($lead_data['pickup_zip']); ?>">
                </div>
                <div class="form-group">
                    <label for="delivery_city">Delivery City:</label>
                    <input type="text" id="delivery_city" name="delivery_city" value="<?php echo htmlspecialchars($lead_data['delivery_city']); ?>">
                </div>
                <div class="form-group">
                    <label for="delivery_state">Delivery State:</label>
                    <input type="text" id="delivery_state" name="delivery_state" value="<?php echo htmlspecialchars($lead_data['delivery_state']); ?>">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group span-3-columns">
                    <label for="formatted_message">Formatted Message:</label>
                    <textarea id="formatted_message" name="formatted_message" rows="8"><?php echo htmlspecialchars($lead_data['formatted_message']); ?></textarea>
                </div>
                <div class="form-group buttons-stacked">
                    <label for="delivery_zip">Delivery Zip:</label>
                    <input type="text" id="delivery_zip" name="delivery_zip" value="<?php echo htmlspecialchars($lead_data['delivery_zip']); ?>">
                    <div class="form-buttons">
                        <button type="submit">Update Lead</button>
                        <button type="button" class="cancel-btn" onclick="window.location.href='view_leads.php';">Cancel</button>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <footer>Powered by Desired Technologies</footer>
    <script>
        window.onload = function() {
            const urlParams = new URLSearchParams(window.location.search);
            const status = urlParams.get('status');
            const message = urlParams.get('message');
            if (status && message) {
                const statusDiv = document.getElementById('statusMessage');
                statusDiv.textContent = decodeURIComponent(message);
                statusDiv.className = `status-message ${status === 'success' ? 'success' : 'error'}`;
                statusDiv.style.display = 'block';
                setTimeout(() => statusDiv.style.display = 'none', 5000);
            }
        };
    </script>
</body>
</html>